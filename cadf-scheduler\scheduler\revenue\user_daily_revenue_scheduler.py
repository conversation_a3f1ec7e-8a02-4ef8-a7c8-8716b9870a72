import time
from datetime import datetime, date
from typing import List, Dict, Set
from collections import defaultdict

from models.models import AccountTrafficMetricsSnapshot, UserDailyRevenue
from omni.log.log import olog
from omni.mongo.mongo_client import init_models
from omni.scheduler.base_schedule import BaseScheduler
from omni.scheduler.schedule_register import register_scheduler


def calculate_daily_revenue(yesterday_snapshot: AccountTrafficMetricsSnapshot,
                          previous_snapshots: List[AccountTrafficMetricsSnapshot]) -> int:
    """
    计算每日收益

    Args:
        yesterday_snapshot: 昨天的快照数据
        previous_snapshots: 昨天之前的快照数据列表（按时间倒序排列）

    Returns:
        int: 每日收益（单位：分）
    """
    # 获取昨天的浏览量
    yesterday_views = yesterday_snapshot.view_count if yesterday_snapshot.view_count else 0

    # 确定基准浏览量
    if previous_snapshots:
        # 使用最新的历史数据作为基准
        base_snapshot = previous_snapshots[0]
        base_views = base_snapshot.view_count if base_snapshot.view_count else 0
    else:
        # 没有历史数据，视为新任务，基准为0
        base_views = 0

    # 计算增量流量
    incremental_views = max(0, yesterday_views - base_views)

    # 计算收益（增量流量 * 0.001元，转换为分）
    daily_revenue = int(incremental_views * 0.001 * 100)

    return daily_revenue


async def execute_task() -> None:
    """
    计算用户每日任务收益

    逻辑：
    1. 获取昨天的 AccountTrafficMetricsSnapshot 数据
    2. 对于每个有昨天数据的任务，查找昨天之前的最新一个数据作为基准
    3. 计算增量流量（昨天的浏览量 - 基准浏览量）
    4. 根据增量流量计算收益（增量流量 * 0.001元）
    5. 创建或更新 UserDailyRevenue 记录（记录日期为昨天）
    """
    olog.info("开始执行用户每日任务收益计算")

    # 获取昨天的零点时间戳
    today = date.today()
    today_timestamp = int(datetime.combine(today, datetime.min.time()).timestamp())
    yesterday_timestamp = today_timestamp - (24 * 60 * 60)

    olog.debug(f"今天零点时间戳: {today_timestamp}, 昨天零点时间戳: {yesterday_timestamp}")

    # 查询昨天的快照数据
    yesterday_snapshots = await AccountTrafficMetricsSnapshot.find(
        AccountTrafficMetricsSnapshot.snapshot_at >= yesterday_timestamp,
        AccountTrafficMetricsSnapshot.snapshot_at < today_timestamp
    ).to_list()

    olog.debug(f"查询到昨天快照数据: {len(yesterday_snapshots)} 条")

    # 按 (user_id, promotion_task_detail_id) 组合分组昨天的快照数据
    yesterday_data: Dict[tuple, AccountTrafficMetricsSnapshot] = {}

    # 处理昨天的数据
    for snapshot in yesterday_snapshots:
        if snapshot.user_id and snapshot.promotion_task_detail_id:
            key = (snapshot.user_id, snapshot.promotion_task_detail_id)
            yesterday_data[key] = snapshot

    olog.debug(f"昨天有效快照数据: {len(yesterday_data)} 条")

    # 计算增量流量和收益
    processed_count = 0
    total_revenue = 0

    # 只处理昨天有数据的任务
    for (user_id, task_detail_id), yesterday_snapshot in yesterday_data.items():
        # 如果昨天没有数据，跳过（无法计算昨天的收益）
        if not yesterday_snapshot:
            olog.debug(f"用户 {user_id} 任务 {task_detail_id}: 昨天无快照数据，跳过")
            continue

        # 查找昨天之前的最新一个数据作为基准
        olog.debug(f"用户 {user_id} 任务 {task_detail_id}: 查找昨天之前的最新基准数据")

        previous_snapshots = await AccountTrafficMetricsSnapshot.find(
            AccountTrafficMetricsSnapshot.user_id == user_id,
            AccountTrafficMetricsSnapshot.promotion_task_detail_id == task_detail_id,
            AccountTrafficMetricsSnapshot.snapshot_at < yesterday_timestamp
        ).sort([("snapshot_at", -1)]).limit(1).to_list()

        # 使用新的计算收益方法
        daily_revenue = calculate_daily_revenue(yesterday_snapshot, previous_snapshots)

        # 记录调试信息
        yesterday_views = yesterday_snapshot.view_count if yesterday_snapshot.view_count else 0
        if previous_snapshots:
            base_snapshot = previous_snapshots[0]
            base_views = base_snapshot.view_count if base_snapshot.view_count else 0
            days_gap = (yesterday_timestamp - base_snapshot.snapshot_at) // (24 * 60 * 60)
            olog.debug(f"用户 {user_id} 任务 {task_detail_id}: 找到 {days_gap} 天前的基准数据，浏览量={base_views}")
        else:
            base_views = 0
            days_gap = 0
            olog.debug(f"用户 {user_id} 任务 {task_detail_id}: 昨天之前无任何数据，视为新任务")

        incremental_views = max(0, yesterday_views - base_views)

        if daily_revenue <= 0:
            # 如果没有增量或增量为负数，跳过
            olog.debug(f"用户 {user_id} 任务 {task_detail_id}: 无增量流量或增量为负，跳过（基准:{base_views}, 昨天:{yesterday_views}）")
            continue

        # 检查是否已存在昨天的收益记录
        existing_revenue = await UserDailyRevenue.find_one(
            UserDailyRevenue.user_id == user_id,
            UserDailyRevenue.promotion_task_detail_id == task_detail_id,
            UserDailyRevenue.date == yesterday_timestamp
        )

        current_time = int(time.time())

        if existing_revenue:
            # 更新已存在的记录
            await existing_revenue.update({
                "$set": {
                    "daily_revenue": daily_revenue,
                    "status": "未结算",
                    "settled_at": None
                }
            })
            olog.debug(f"更新用户 {user_id} 任务 {task_detail_id} 的收益记录: {daily_revenue}分")
        else:
            # 创建新的收益记录（记录日期为昨天）
            new_revenue = UserDailyRevenue(
                user_id=user_id,
                promotion_task_detail_id=task_detail_id,
                date=yesterday_timestamp,
                daily_revenue=daily_revenue,
                status="未结算",
                settled_at=None,
                created_at=current_time
            )
            await new_revenue.insert()
            olog.debug(f"创建用户 {user_id} 任务 {task_detail_id} 的收益记录: {daily_revenue}分")

        processed_count += 1
        total_revenue += daily_revenue

        # 构建详细的日志信息
        if days_gap == 0:
            base_info = "新任务"
        else:
            base_info = f"{days_gap}天前基准数据"
        olog.debug(f"用户 {user_id} 任务 {task_detail_id}: {base_info}浏览量={base_views}, 昨天浏览量={yesterday_views}, 增量={incremental_views}, 收益={daily_revenue}分")

    olog.info(f"用户每日任务收益计算完成，处理了 {processed_count} 条记录，总收益: {total_revenue}分 ({total_revenue/100:.2f}元)")


@register_scheduler(trigger="cron", hour="7", minute="0")
class UserDailyRevenueScheduler(BaseScheduler):
    async def run_task(self) -> None:
        """
        执行每日任务收益计算任务
        
        调度频率：每天早上7点执行一次
        """
        olog.info("开始执行每日任务收益计算任务")
        await execute_task()
        olog.info("每日任务收益计算任务执行完毕")


if __name__ == "__main__":
    import asyncio


    async def main():
        await init_models()
        await execute_task()


    asyncio.run(main())
